@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');

/* Design System CSS Variables */
:root {
  /* Color System */
  --color-primary: 59 130 246;
  --color-primary-foreground: 255 255 255;
  --color-secondary: 100 116 139;
  --color-secondary-foreground: 15 23 42;
  --color-surface: 255 255 255;
  --color-surface-foreground: 15 23 42;
  --color-sidebar: 15 23 42;
  --color-sidebar-foreground: 248 250 252;

  /* Gradient System */
  --gradient-primary: linear-gradient(135deg, rgb(59 130 246) 0%, rgb(29 78 216) 100%);
  --gradient-secondary: linear-gradient(135deg, rgb(100 116 139) 0%, rgb(51 65 85) 100%);
  --gradient-surface: linear-gradient(135deg, rgb(248 250 252) 0%, rgb(226 232 240) 100%);
  --gradient-sidebar: linear-gradient(180deg, rgb(15 23 42) 0%, rgb(30 41 59) 100%);
  --gradient-card: linear-gradient(135deg, rgb(255 255 255) 0%, rgb(248 250 252) 100%);
  --gradient-hero: linear-gradient(135deg, rgb(239 246 255) 0%, rgb(219 234 254) 50%, rgb(191 219 254) 100%);

  /* Shadow System */
  --shadow-card: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-card-hover: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --shadow-sidebar: 2px 0 10px 0 rgb(0 0 0 / 0.1);

  /* Border Radius System */
  --radius-card: 0.75rem;
  --radius-button: 0.5rem;
  --radius-input: 0.5rem;
  --radius-modal: 1rem;

  /* Spacing System */
  --spacing-card: 1.5rem;
  --spacing-section: 2rem;
  --spacing-page: 2rem;
}

/* Dark Mode Variables */
.dark {
  --color-surface: 30 41 59;
  --color-surface-foreground: 248 250 252;
  --gradient-card: linear-gradient(135deg, rgb(30 41 59) 0%, rgb(51 65 85) 100%);
  --gradient-hero: linear-gradient(135deg, rgb(15 23 42) 0%, rgb(30 41 59) 50%, rgb(51 65 85) 100%);
  --shadow-card: 0 4px 6px -1px rgb(0 0 0 / 0.3), 0 2px 4px -2px rgb(0 0 0 / 0.3);
  --shadow-card-hover: 0 20px 25px -5px rgb(0 0 0 / 0.3), 0 8px 10px -6px rgb(0 0 0 / 0.3);
}

@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
    scroll-behavior: smooth;
  }

  body {
    @apply bg-gradient-to-br from-gray-50 to-blue-50/30 text-gray-900 antialiased;
    @apply dark:from-gray-900 dark:to-gray-800 dark:text-gray-100;
    transition: background-color 0.3s ease, color 0.3s ease;
  }

  /* Smooth transitions for theme switching */
  * {
    transition-property: background-color, border-color, color, fill, stroke, box-shadow;
    transition-duration: 0.15s;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  }
}

@layer components {
  /* Enhanced Card System */
  .card {
    @apply bg-white dark:bg-slate-800 rounded-card shadow-card border border-gray-200/50 dark:border-gray-700/50;
    @apply hover:shadow-card-hover transition-all duration-300;
    background: var(--gradient-card);
  }

  .card-compact {
    @apply card p-4;
  }

  .card-default {
    @apply card p-6;
  }

  .card-large {
    @apply card p-8;
  }

  .card-interactive {
    @apply card cursor-pointer hover:-translate-y-1 hover:scale-[1.02];
    @apply active:scale-[0.98] transition-all duration-200;
  }

  /* Enhanced Button System */
  .btn {
    @apply inline-flex items-center justify-center rounded-button text-sm font-medium;
    @apply transition-all duration-200 focus-visible:outline-none focus-visible:ring-2;
    @apply focus-visible:ring-primary-500 focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none;
    @apply active:scale-95 hover:scale-105;
  }

  .btn-primary {
    @apply btn bg-gradient-to-r from-primary-600 to-primary-700 text-white;
    @apply hover:from-primary-700 hover:to-primary-800 shadow-md hover:shadow-lg;
    @apply h-10 py-2 px-4;
  }

  .btn-secondary {
    @apply btn bg-gradient-to-r from-secondary-100 to-secondary-200 text-secondary-900;
    @apply hover:from-secondary-200 hover:to-secondary-300 border border-secondary-300;
    @apply dark:from-secondary-700 dark:to-secondary-600 dark:text-secondary-100;
    @apply h-10 py-2 px-4;
  }

  .btn-outline {
    @apply btn border border-gray-300 bg-white hover:bg-gray-50 hover:text-gray-900;
    @apply dark:border-gray-600 dark:bg-gray-800 dark:hover:bg-gray-700 dark:text-gray-100;
    @apply h-10 py-2 px-4 shadow-sm hover:shadow-md;
  }

  .btn-ghost {
    @apply btn hover:bg-gray-100 hover:text-gray-900 dark:hover:bg-gray-700 dark:hover:text-gray-100;
    @apply h-10 py-2 px-4;
  }

  .btn-sm {
    @apply h-9 px-3 text-xs;
  }

  .btn-lg {
    @apply h-11 px-8 text-base;
  }

  /* Gradient Backgrounds */
  .bg-gradient-primary {
    background: var(--gradient-primary);
  }

  .bg-gradient-secondary {
    background: var(--gradient-secondary);
  }

  .bg-gradient-surface {
    background: var(--gradient-surface);
  }

  .bg-gradient-sidebar {
    background: var(--gradient-sidebar);
  }

  .bg-gradient-hero {
    background: var(--gradient-hero);
  }

  /* Enhanced Input System */
  .input {
    @apply flex h-10 w-full rounded-input border border-gray-300 bg-white px-3 py-2 text-sm;
    @apply file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500;
    @apply focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary-500 focus-visible:ring-offset-2;
    @apply disabled:cursor-not-allowed disabled:opacity-50 transition-all duration-200;
    @apply dark:border-gray-600 dark:bg-gray-800 dark:text-gray-100 dark:placeholder:text-gray-400;
    @apply hover:border-gray-400 focus:border-primary-500;
  }

  .input-error {
    @apply input border-red-500 focus-visible:ring-red-500;
  }

  /* Enhanced Card Content Structure */
  .card-header {
    @apply flex flex-col space-y-1.5 p-6 pb-4;
  }

  .card-title {
    @apply text-xl font-semibold leading-none tracking-tight text-gray-900 dark:text-gray-100;
  }

  .card-description {
    @apply text-sm text-gray-600 dark:text-gray-400;
  }

  .card-content {
    @apply px-6 pb-6;
  }

  .card-footer {
    @apply flex items-center px-6 pb-6 pt-0;
  }

  /* Enhanced Badge System */
  .badge {
    @apply inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold;
    @apply transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
    @apply hover:scale-105 active:scale-95;
  }

  .badge-default {
    @apply badge border-transparent bg-gradient-to-r from-primary-600 to-primary-700 text-white;
    @apply hover:from-primary-700 hover:to-primary-800 shadow-sm hover:shadow-md;
  }

  .badge-secondary {
    @apply badge border-transparent bg-gradient-to-r from-secondary-100 to-secondary-200 text-secondary-900;
    @apply hover:from-secondary-200 hover:to-secondary-300 dark:from-secondary-700 dark:to-secondary-600 dark:text-secondary-100;
  }

  .badge-success {
    @apply badge border-transparent bg-gradient-to-r from-green-500 to-green-600 text-white;
    @apply hover:from-green-600 hover:to-green-700;
  }

  .badge-warning {
    @apply badge border-transparent bg-gradient-to-r from-yellow-500 to-yellow-600 text-white;
    @apply hover:from-yellow-600 hover:to-yellow-700;
  }

  .badge-error {
    @apply badge border-transparent bg-gradient-to-r from-red-500 to-red-600 text-white;
    @apply hover:from-red-600 hover:to-red-700;
  }

  .badge-outline {
    @apply badge text-gray-900 border-gray-300 hover:bg-gray-50;
    @apply dark:text-gray-100 dark:border-gray-600 dark:hover:bg-gray-700;
  }

  /* Enhanced Loading System */
  .spinner {
    @apply animate-spin rounded-full border-2 border-gray-300 border-t-primary-600;
  }

  .spinner-lg {
    @apply spinner w-8 h-8;
  }

  .spinner-md {
    @apply spinner w-6 h-6;
  }

  .spinner-sm {
    @apply spinner w-4 h-4;
  }

  /* Sidebar Styles */
  .sidebar {
    @apply fixed left-0 top-0 h-full w-64 bg-gradient-sidebar shadow-sidebar;
    @apply transform transition-transform duration-300 ease-in-out z-40;
  }

  .sidebar-collapsed {
    @apply sidebar w-16;
  }

  .sidebar-item {
    @apply flex items-center px-4 py-3 text-slate-200 hover:bg-white/10;
    @apply transition-all duration-200 cursor-pointer rounded-lg mx-2;
  }

  .sidebar-item-active {
    @apply sidebar-item bg-white/20 text-white font-medium;
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }

  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  /* Glass Effect */
  .glass {
    @apply backdrop-blur-md bg-white/80 dark:bg-gray-900/80;
    @apply border border-white/20 dark:border-gray-700/20;
  }

  /* Gradient Text */
  .text-gradient-primary {
    @apply bg-gradient-to-r from-primary-600 to-primary-800 bg-clip-text text-transparent;
  }

  .text-gradient-secondary {
    @apply bg-gradient-to-r from-secondary-600 to-secondary-800 bg-clip-text text-transparent;
  }

  /* Animation Utilities */
  .animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out forwards;
  }

  .animate-fade-in-down {
    animation: fadeInDown 0.6s ease-out forwards;
  }

  .animate-scale-in {
    animation: scaleIn 0.3s ease-out forwards;
  }

  /* Hover Effects */
  .hover-lift {
    @apply transition-all duration-300 hover:-translate-y-2 hover:shadow-xl;
  }

  .hover-glow {
    @apply transition-all duration-300 hover:shadow-lg hover:shadow-primary-500/25;
  }

  /* Enhanced Layout Utilities */
  .container-app {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }

  .section-padding {
    @apply py-8 sm:py-12 lg:py-16;
  }

  .card-grid {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 sm:gap-6;
  }

  .card-grid-large {
    @apply grid grid-cols-1 lg:grid-cols-2 gap-6 lg:gap-8;
  }

  .card-grid-compact {
    @apply grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4;
  }

  /* Responsive Sidebar */
  .sidebar {
    @apply fixed left-0 top-0 h-full w-64 bg-gradient-sidebar shadow-sidebar;
    @apply transform transition-transform duration-300 ease-in-out z-40;
  }

  .sidebar-mobile {
    @apply lg:translate-x-0;
  }

  /* Responsive Text */
  .text-responsive-xl {
    @apply text-2xl sm:text-3xl lg:text-4xl xl:text-5xl;
  }

  .text-responsive-lg {
    @apply text-xl sm:text-2xl lg:text-3xl;
  }

  .text-responsive-md {
    @apply text-lg sm:text-xl lg:text-2xl;
  }

  /* Responsive Spacing */
  .space-responsive {
    @apply space-y-4 sm:space-y-6 lg:space-y-8;
  }

  .gap-responsive {
    @apply gap-4 sm:gap-6 lg:gap-8;
  }
}

/* Additional Keyframes */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
