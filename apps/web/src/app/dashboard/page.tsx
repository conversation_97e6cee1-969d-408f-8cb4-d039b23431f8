'use client';

import { apiClient } from '@/lib/api';
import { useAuth, withAuth } from '@/lib/auth';
import { formatCurrency } from '@/lib/utils';
import { useQuery } from '@tanstack/react-query';
import {
    Activity,
    AlertTriangle,
    BarChart3,
    Bell,
    Calculator,
    Clock,
    FileText,
    Search,
    TrendingDown,
    TrendingUp,
    Zap
} from 'lucide-react';

function DashboardPage() {
  const { user, logout } = useAuth();

  const { data: propertyStats, isLoading: statsLoading } = useQuery({
    queryKey: ['property-statistics'],
    queryFn: () => apiClient.getPropertyStatistics(),
  });

  const { data: recentProperties, isLoading: propertiesLoading } = useQuery({
    queryKey: ['recent-properties'],
    queryFn: () => apiClient.getProperties({ limit: 6, sortBy: 'createdAt', sortOrder: 'desc' }),
  });

  // Mock data for advanced dashboard features
  const mockDashboardData = {
    portfolio: {
      totalValue: 3850000,
      monthlyChange: { amount: 125000, percentage: 3.4 },
      properties: [
        { id: '1', address: '123 Brunswick St, New Farm', value: 1275000, change: 2.1, status: 'tracked' },
        { id: '2', address: '45 James St, Fortitude Valley', value: 950000, change: -1.2, status: 'tracked' },
        { id: '3', address: '78 Ann St, Brisbane City', value: 1625000, change: 4.8, status: 'tracked' }
      ],
      bestPerformer: { address: '78 Ann St, Brisbane City', change: 4.8 },
      worstPerformer: { address: '45 James St, Fortitude Valley', change: -1.2 },
      activeAlerts: 7
    },
    marketMovers: {
      growing: [
        { name: 'New Farm', state: 'QLD', growth: 8.5, median: 1150000 },
        { name: 'Teneriffe', state: 'QLD', growth: 7.2, median: 980000 },
        { name: 'Kangaroo Point', state: 'QLD', growth: 6.8, median: 750000 },
        { name: 'West End', state: 'QLD', growth: 6.1, median: 820000 },
        { name: 'Paddington', state: 'QLD', growth: 5.9, median: 1050000 }
      ],
      declining: [
        { name: 'Surfers Paradise', state: 'QLD', growth: -2.1, median: 650000 },
        { name: 'Broadbeach', state: 'QLD', growth: -1.8, median: 720000 },
        { name: 'Main Beach', state: 'QLD', growth: -1.5, median: 890000 }
      ]
    },
    insights: [
      {
        type: 'opportunity',
        title: 'New Infrastructure Project',
        description: 'Light rail extension near your New Farm property could increase value by 15-20%',
        impact: 'high',
        timeAgo: '2 hours ago',
        icon: 'zap'
      },
      {
        type: 'alert',
        title: 'Price Alert Triggered',
        description: 'Property at 45 James St dropped below your $960k threshold',
        impact: 'medium',
        timeAgo: '5 hours ago',
        icon: 'bell'
      },
      {
        type: 'insight',
        title: 'Market Analysis',
        description: 'Inner Brisbane suburbs showing strong growth momentum this quarter',
        impact: 'low',
        timeAgo: '1 day ago',
        icon: 'activity'
      },
      {
        type: 'risk',
        title: 'Market Risk Update',
        description: 'Interest rate changes may affect property values in Q2 2025',
        impact: 'medium',
        timeAgo: '2 days ago',
        icon: 'alert'
      }
    ]
  };

  return (
    <div className="min-h-screen bg-gradient-hero">
      <div className="container-app section-padding">
        {/* Welcome Section */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-3">
                Welcome back, {user?.firstName || 'User'}!
              </h1>
              <p className="text-xl text-gray-600 max-w-2xl">
                Get instant property valuations and development insights with AI-powered analysis.
              </p>
            </div>
            <div className="hidden lg:flex items-center space-x-2 bg-green-50 px-4 py-2 rounded-full">
              <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
              <span className="text-green-700 font-medium">Live Data</span>
            </div>
          </div>
        </div>

        {/* Main Valuation Section - Prominent */}
        <div className="card-large mb-8 shadow-xl hover:shadow-2xl">
          <div className="text-center mb-8">
            <h2 className="text-3xl font-bold text-gray-900 mb-3">
              New Property Valuation
            </h2>
            <p className="text-lg text-gray-600">
              Enter an address to analyse the property value and development potential
            </p>
          </div>
          <div className="relative">
            <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-6 h-6" />
            <input
              type="text"
              placeholder="Enter property address (e.g. 123 Collins Street, Melbourne VIC)"
              className="input w-full pl-14 pr-32 py-4 text-lg shadow-sm"
            />
            <button
              type="button"
              className="absolute right-2 top-1/2 transform -translate-y-1/2 btn-primary px-6 py-2"
            >
              Analyse
            </button>
          </div>
        </div>

        {/* Valuation History and Quick Actions */}
        <div className="card-grid-large mb-8">
          <div className="card-default">
            <div className="flex items-center mb-4">
              <Clock className="w-5 h-5 text-gray-400 mr-2" />
              <h3 className="text-lg font-semibold text-gray-900">Recent Valuations</h3>
            </div>
            <div className="text-center py-8">
              <Calculator className="w-12 h-12 text-gray-300 mx-auto mb-3" />
              <p className="text-gray-500">No valuations yet</p>
              <p className="text-sm text-gray-400">Your recent property valuations will appear here</p>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="card-default">
            <h3 className="text-lg font-semibold text-gray-900 mb-6">Quick Actions</h3>
            <div className="grid grid-cols-2 gap-4">
              <button className="flex items-center space-x-3 p-4 border border-gray-200 rounded-card hover:bg-gray-50 hover-lift transition-all duration-200">
                <Calculator className="w-5 h-5 text-green-600" />
                <div className="text-left">
                  <p className="font-medium text-gray-900">Quick Valuation</p>
                  <p className="text-sm text-gray-600">Instant property value</p>
                </div>
              </button>

              <button className="flex items-center space-x-3 p-4 border border-gray-200 rounded-card hover:bg-gray-50 hover-lift transition-all duration-200">
                <BarChart3 className="w-5 h-5 text-blue-600" />
                <div className="text-left">
                  <p className="font-medium text-gray-900">Development Analysis</p>
                  <p className="text-sm text-gray-600">Assess development potential</p>
                </div>
              </button>

              <button className="flex items-center space-x-3 p-4 border border-gray-200 rounded-card hover:bg-gray-50 hover-lift transition-all duration-200">
                <FileText className="w-5 h-5 text-purple-600" />
                <div className="text-left">
                  <p className="font-medium text-gray-900">Valuation Report</p>
                  <p className="text-sm text-gray-600">Detailed analysis report</p>
                </div>
              </button>

              <button className="flex items-center space-x-3 p-4 border border-gray-200 rounded-card hover:bg-gray-50 hover-lift transition-all duration-200">
                <Bell className="w-5 h-5 text-yellow-600" />
                <div className="text-left">
                  <p className="font-medium text-gray-900">Value Alerts</p>
                  <p className="text-sm text-gray-600">Track value changes</p>
                </div>
              </button>
            </div>
          </div>
        </div>

        {/* Portfolio Summary & Market Movers */}
        <div className="card-grid-large mb-8">
          {/* Compact Portfolio Summary */}
          <div className="card-default">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold text-gray-900">Portfolio Summary</h3>
              <div className="flex items-center space-x-2 bg-green-50 px-3 py-1 rounded-full">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                <span className="text-xs font-medium text-green-700">Live</span>
              </div>
            </div>

            <div className="text-center mb-6">
              <div className="text-3xl font-bold bg-gradient-to-r from-primary-600 to-indigo-600 bg-clip-text text-transparent mb-2">
                {formatCurrency(mockDashboardData.portfolio.totalValue)}
              </div>
              <p className="text-sm text-gray-600">Total Portfolio Value</p>
              <div className="flex items-center justify-center mt-2">
                <TrendingUp className="w-4 h-4 mr-1 text-green-600" />
                <span className="text-sm font-medium text-green-600">
                  +{mockDashboardData.portfolio.monthlyChange.percentage}% this month
                </span>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4 mb-4">
              <div className="text-center p-3 bg-gray-50 rounded-lg">
                <div className="text-lg font-bold text-gray-900">{mockDashboardData.portfolio.properties.length}</div>
                <div className="text-xs text-gray-600">Properties</div>
              </div>
              <div className="text-center p-3 bg-gray-50 rounded-lg">
                <div className="text-lg font-bold text-gray-900">{mockDashboardData.portfolio.activeAlerts}</div>
                <div className="text-xs text-gray-600">Active Alerts</div>
              </div>
            </div>

            <button className="w-full text-primary-600 hover:text-primary-700 text-sm font-medium">
              View Full Portfolio →
            </button>
          </div>

          {/* Market Movers */}
          <div className="card-default">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold text-gray-900">Market Movers</h3>
              <button className="text-primary-600 hover:text-primary-700 text-sm font-medium">View All</button>
            </div>

            <div className="space-y-4">
              {/* Top Growing Suburbs */}
              <div>
                <h4 className="text-sm font-medium text-gray-700 mb-3 flex items-center">
                  <TrendingUp className="w-4 h-4 mr-2 text-green-600" />
                  Top Growing
                </h4>
                <div className="space-y-2">
                  {mockDashboardData.marketMovers.growing.slice(0, 2).map((suburb, index) => (
                    <div key={index} className="flex items-center justify-between p-2 bg-green-50 rounded-lg">
                      <div>
                        <p className="text-sm font-medium text-gray-900">{suburb.name}, {suburb.state}</p>
                        <p className="text-xs text-gray-600">{formatCurrency(suburb.median)}</p>
                      </div>
                      <span className="text-sm font-bold text-green-600">+{suburb.growth}%</span>
                    </div>
                  ))}
                </div>
              </div>

              {/* Watch List */}
              <div>
                <h4 className="text-sm font-medium text-gray-700 mb-3 flex items-center">
                  <TrendingDown className="w-4 h-4 mr-2 text-red-600" />
                  Watch List
                </h4>
                <div className="space-y-2">
                  {mockDashboardData.marketMovers.declining.slice(0, 2).map((suburb, index) => (
                    <div key={index} className="flex items-center justify-between p-2 bg-red-50 rounded-lg">
                      <div>
                        <p className="text-sm font-medium text-gray-900">{suburb.name}, {suburb.state}</p>
                        <p className="text-xs text-gray-600">{formatCurrency(suburb.median)}</p>
                      </div>
                      <span className="text-sm font-bold text-red-600">{suburb.growth}%</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Personal Insights */}
        <div className="card-default mb-8">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-gray-900">Recent Insights</h3>
            <button className="text-primary-600 hover:text-primary-700 text-sm font-medium">View All</button>
          </div>

          <div className="space-y-3">
            {mockDashboardData.insights.slice(0, 3).map((insight, index) => (
              <div key={index} className={`p-3 rounded-card border-l-4 hover-lift transition-all duration-200 ${
                insight.type === 'opportunity' ? 'bg-green-50 border-green-500' :
                insight.type === 'alert' ? 'bg-yellow-50 border-yellow-500' :
                insight.type === 'risk' ? 'bg-red-50 border-red-500' :
                'bg-blue-50 border-blue-500'
              }`}>
                <div className="flex items-start space-x-3">
                  <div className={`w-6 h-6 rounded-full flex items-center justify-center ${
                    insight.type === 'opportunity' ? 'bg-green-100' :
                    insight.type === 'alert' ? 'bg-yellow-100' :
                    insight.type === 'risk' ? 'bg-red-100' :
                    'bg-blue-100'
                  }`}>
                    {insight.icon === 'zap' && <Zap className="w-3 h-3 text-green-600" />}
                    {insight.icon === 'bell' && <Bell className="w-3 h-3 text-yellow-600" />}
                    {insight.icon === 'activity' && <Activity className="w-3 h-3 text-blue-600" />}
                    {insight.icon === 'alert' && <AlertTriangle className="w-3 h-3 text-red-600" />}
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center justify-between mb-1">
                      <h4 className="text-sm font-semibold text-gray-900">{insight.title}</h4>
                      <span className="text-xs text-gray-500">{insight.timeAgo}</span>
                    </div>
                    <p className="text-xs text-gray-700">{insight.description}</p>
                    <span className={`inline-block mt-1 text-xs px-2 py-1 rounded-full ${
                      insight.impact === 'high' ? 'bg-red-100 text-red-800' :
                      insight.impact === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-green-100 text-green-800'
                    }`}>
                      {insight.impact} impact
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>


      </div>
    </div>
  );
}

export default withAuth(DashboardPage);
