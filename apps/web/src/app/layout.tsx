import { Header } from '@/components/ui/header';
import { ComparisonProvider } from '@/contexts/ComparisonContext';
import { AuthProvider } from '@/lib/auth';
import { QueryProvider } from '@/lib/query-client';
import type { Metadata } from 'next';
import { Inter } from 'next/font/google';
import { Toaster } from 'react-hot-toast';
import './globals.css';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'Revalu - Property Intelligence Platform',
  description: 'Advanced property valuation and market intelligence platform',
  keywords: ['property', 'real estate', 'valuation', 'market analysis', 'Australia'],
  authors: [{ name: 'Revalu Team' }],
  robots: 'index, follow',
  openGraph: {
    title: 'Revalu - Property Intelligence Platform',
    description: 'Advanced property valuation and market intelligence platform',
    type: 'website',
    locale: 'en_AU',
  },
};

export const viewport = {
  width: 'device-width',
  initialScale: 1,
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  // Import components dynamically to avoid auto-formatter issues
  const { ThemeProvider } = require('@/contexts/ThemeContext');
  const { SidebarNavigation } = require('@/components/ui/sidebar-navigation');

  return (
    <html lang="en" className="h-full">
      <body className={`${inter.className} h-full`}>
        <QueryProvider>
          <AuthProvider>
            <ThemeProvider>
              <ComparisonProvider>
                <div className="flex h-full">
                  <SidebarNavigation />
                  <div className="flex-1 flex flex-col lg:ml-64">
                    <Header />
                    <main className="flex-1 bg-gradient-hero min-h-screen">
                      {children}
                    </main>
                  </div>
                </div>
              </ComparisonProvider>
              <Toaster
                position="top-right"
                toastOptions={{
                  duration: 4000,
                  style: {
                    background: '#363636',
                    color: '#fff',
                  },
                  success: {
                    duration: 3000,
                    iconTheme: {
                      primary: '#10b981',
                      secondary: '#fff',
                    },
                  },
                  error: {
                    duration: 5000,
                    iconTheme: {
                      primary: '#ef4444',
                      secondary: '#fff',
                    },
                  },
                }}
              />
            </ThemeProvider>
          </AuthProvider>
        </QueryProvider>
      </body>
    </html>
  );
}
