'use client';

import { ThemeToggle } from '@/contexts/ThemeContext';
import { useAuth } from '@/lib/auth';
import { cn } from '@/lib/utils';
import {
    BarChart3,
    ChevronLeft,
    FileText,
    Home,
    MapPin,
    Search,
    Settings,
    TrendingUp,
    User,
    Wallet
} from 'lucide-react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useState } from 'react';

interface NavigationItem {
  name: string;
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  badge?: string;
  children?: NavigationItem[];
}

const navigationItems: NavigationItem[] = [
  {
    name: 'Dashboard',
    href: '/dashboard',
    icon: Home,
  },
  {
    name: 'Property Valuations',
    href: '/properties',
    icon: Search,
  },
  {
    name: 'Market Intelligence',
    href: '/market',
    icon: TrendingUp,
    children: [
      {
        name: 'Market Dashboard',
        href: '/market',
        icon: BarChart3,
      },
      {
        name: 'Suburb Profiles',
        href: '/suburbs',
        icon: MapPin,
      },
    ],
  },
  {
    name: 'Portfolio',
    href: '/portfolio',
    icon: Wallet,
  },
  {
    name: 'Reports',
    href: '/reports',
    icon: FileText,
  },
];

const bottomNavigationItems: NavigationItem[] = [
  {
    name: 'Settings',
    href: '/settings',
    icon: Settings,
  },
  {
    name: 'Profile',
    href: '/profile',
    icon: User,
  },
];

interface SidebarNavigationProps {
  className?: string;
}

export function SidebarNavigation({ className }: SidebarNavigationProps) {
  const { user, isAuthenticated } = useAuth();
  const pathname = usePathname();
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [hoveredItem, setHoveredItem] = useState<string | null>(null);

  // Don't render sidebar for unauthenticated users
  if (!isAuthenticated) {
    return null;
  }

  const isActive = (href: string) => {
    if (href === '/dashboard') {
      return pathname === '/dashboard';
    }
    return pathname?.startsWith(href) || false;
  };

  const isActiveGroup = (items: NavigationItem[]) => {
    return items.some(item => isActive(item.href));
  };

  return (
    <>
      {/* Sidebar */}
      <div
        className={cn(
          'sidebar',
          isCollapsed ? 'sidebar-collapsed' : '',
          className
        )}
        onMouseEnter={() => setIsCollapsed(false)}
        onMouseLeave={() => setIsCollapsed(true)}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-white/10">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-gradient-to-br from-primary-500 to-primary-700 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-sm">R</span>
            </div>
            {!isCollapsed && (
              <span className="text-xl font-bold text-white">Revalu</span>
            )}
          </div>
          
          {!isCollapsed && (
            <button
              onClick={() => setIsCollapsed(!isCollapsed)}
              className="p-1 rounded-md hover:bg-white/10 transition-colors"
            >
              <ChevronLeft className="w-4 h-4 text-white/70" />
            </button>
          )}
        </div>

        {/* User Info */}
        <div className="p-4 border-b border-white/10">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-gradient-to-br from-primary-400 to-primary-600 rounded-full flex items-center justify-center">
              <span className="text-white font-medium text-sm">
                {user?.firstName?.[0] || user?.email?.[0] || 'U'}
              </span>
            </div>
            {!isCollapsed && (
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-white truncate">
                  {user?.firstName || 'User'}
                </p>
                <p className="text-xs text-white/70 truncate">
                  {user?.email}
                </p>
              </div>
            )}
          </div>
        </div>

        {/* Navigation */}
        <nav className="flex-1 p-2 space-y-1">
          {navigationItems.map((item) => (
            <div key={item.name}>
              <Link
                href={item.href}
                className={cn(
                  'sidebar-item',
                  isActive(item.href) || (item.children && isActiveGroup(item.children))
                    ? 'sidebar-item-active'
                    : ''
                )}
                onMouseEnter={() => setHoveredItem(item.name)}
                onMouseLeave={() => setHoveredItem(null)}
              >
                <item.icon className="w-5 h-5 flex-shrink-0" />
                {!isCollapsed && (
                  <>
                    <span className="ml-3 flex-1">{item.name}</span>
                    {item.badge && (
                      <span className="ml-auto bg-primary-500 text-white text-xs px-2 py-0.5 rounded-full">
                        {item.badge}
                      </span>
                    )}
                  </>
                )}
              </Link>
              
              {/* Submenu */}
              {item.children && !isCollapsed && (
                <div className="ml-8 mt-1 space-y-1">
                  {item.children.map((child) => (
                    <Link
                      key={child.name}
                      href={child.href}
                      className={cn(
                        'flex items-center px-3 py-2 text-sm text-slate-300 hover:text-white hover:bg-white/5 transition-all duration-200 cursor-pointer rounded-md',
                        isActive(child.href) ? 'text-white bg-white/10' : ''
                      )}
                    >
                      <child.icon className="w-4 h-4 mr-3" />
                      {child.name}
                    </Link>
                  ))}
                </div>
              )}
            </div>
          ))}
        </nav>

        {/* Bottom Navigation */}
        <div className="p-2 border-t border-white/10 space-y-1">
          <div className="flex items-center justify-center mb-2">
            <ThemeToggle />
          </div>
          
          {bottomNavigationItems.map((item) => (
            <Link
              key={item.name}
              href={item.href}
              className={cn(
                'sidebar-item',
                isActive(item.href) ? 'sidebar-item-active' : ''
              )}
            >
              <item.icon className="w-5 h-5 flex-shrink-0" />
              {!isCollapsed && <span className="ml-3">{item.name}</span>}
            </Link>
          ))}
        </div>
      </div>

      {/* Main Content Spacer */}
      <div className={cn('transition-all duration-300', isCollapsed ? 'ml-16' : 'ml-64')} />
    </>
  );
}
